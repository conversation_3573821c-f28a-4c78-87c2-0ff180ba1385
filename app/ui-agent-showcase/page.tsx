'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Palette, Monitor, Smartphone, Tablet, Code, Eye, 
  Sparkles, Zap, Layers, Settings, Moon, Sun,
  Play, Copy, RefreshCw, Star, Heart, Award,
  TrendingUp, Users, ShoppingCart, Bell, Search,
  Plus, Minus, Check, X, ArrowRight, ArrowLeft,
  Download, Upload, Edit, Trash2, Filter, Sort
} from 'lucide-react';

// Import UI components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import EnhancedUIAgent from '@/components/ui/EnhancedUIAgent';

const UIAgentShowcase = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState('gaming');

  // Color palettes for comparison
  const colorPalettes = {
    gaming: {
      name: 'Gaming Theme',
      colors: [
        { name: 'Neon Purple', value: '#8b5cf6', description: 'Primary gaming accent' },
        { name: 'Electric Blue', value: '#0066ff', description: 'Secondary highlight' },
        { name: 'Neon Cyan', value: '#00ffff', description: 'Bright accent' },
        { name: 'Hot Pink', value: '#ff00ff', description: 'Alert/danger' },
        { name: 'Neon Green', value: '#00ff00', description: 'Success/active' },
        { name: 'Gaming Dark', value: '#0f172a', description: 'Primary background' },
        { name: 'Gaming Darker', value: '#020617', description: 'Deep background' },
        { name: 'Slate 800', value: '#1e293b', description: 'Card background' }
      ]
    },
    greys: {
      name: 'Grey Variations',
      colors: [
        { name: 'Slate 50', value: '#f8fafc', description: 'Lightest grey' },
        { name: 'Slate 100', value: '#f1f5f9', description: 'Very light grey' },
        { name: 'Slate 200', value: '#e2e8f0', description: 'Light grey' },
        { name: 'Slate 300', value: '#cbd5e1', description: 'Medium light grey' },
        { name: 'Slate 400', value: '#94a3b8', description: 'Medium grey' },
        { name: 'Slate 500', value: '#64748b', description: 'Base grey' },
        { name: 'Slate 600', value: '#475569', description: 'Dark grey' },
        { name: 'Slate 700', value: '#334155', description: 'Darker grey' },
        { name: 'Slate 800', value: '#1e293b', description: 'Very dark grey' },
        { name: 'Slate 900', value: '#0f172a', description: 'Darkest grey' }
      ]
    }
  };

  // Component variations
  const buttonVariants = [
    { name: 'Gaming Primary', className: 'bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white shadow-lg shadow-purple-500/25' },
    { name: 'Neon Outline', className: 'bg-transparent border-2 border-purple-500 text-purple-500 hover:bg-purple-500 hover:text-white animate-pulse-neon' },
    { name: 'Glass Effect', className: 'bg-white/10 backdrop-blur-lg border border-white/20 text-white hover:bg-white/20' },
    { name: 'Cyber Glow', className: 'bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white shadow-lg shadow-cyan-500/25' },
    { name: 'Dark Mode', className: 'bg-slate-800 hover:bg-slate-700 text-white border border-slate-600' },
    { name: 'Light Mode', className: 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-200 shadow-sm' }
  ];

  const cardVariants = [
    {
      name: 'Gaming Card',
      className: 'bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-xl border border-purple-500/30 shadow-2xl shadow-purple-500/20',
      textClass: 'text-white'
    },
    {
      name: 'Neon Glow',
      className: 'bg-slate-900 border-2 border-purple-500 shadow-[0_0_20px_rgba(139,92,246,0.5)] animate-pulse-neon',
      textClass: 'text-white'
    },
    {
      name: 'Glass Morphism',
      className: 'bg-white/10 backdrop-blur-lg border border-white/20 shadow-xl',
      textClass: 'text-white'
    },
    {
      name: 'Dark Slate',
      className: 'bg-slate-800 border border-slate-700 shadow-lg',
      textClass: 'text-white'
    },
    {
      name: 'Light Mode',
      className: 'bg-white border border-gray-200 shadow-md',
      textClass: 'text-gray-900'
    },
    {
      name: 'Gradient Border',
      className: 'bg-slate-900 border-2 border-transparent bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 bg-clip-border p-0.5',
      textClass: 'text-white'
    }
  ];

  const textVariations = [
    { name: 'Gradient Purple-Pink', className: 'bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent font-bold' },
    { name: 'Gradient Cyan-Blue', className: 'bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent font-bold' },
    { name: 'Neon Glow', className: 'text-purple-400 font-bold animate-pulse drop-shadow-[0_0_10px_rgba(139,92,246,0.8)]' },
    { name: 'White Standard', className: 'text-white font-semibold' },
    { name: 'Light Grey', className: 'text-gray-300 font-medium' },
    { name: 'Dark Grey', className: 'text-gray-600 font-medium' },
    { name: 'Gaming Accent', className: 'text-purple-400 font-bold' }
  ];

  const componentShowcase = [
    {
      name: 'Gaming Button',
      code: `<Button className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl shadow-lg shadow-purple-500/25 border border-purple-400/30 hover:shadow-purple-500/40 transition-all duration-300 animate-pulse-neon">
  Gaming Action
</Button>`
    },
    {
      name: 'Points Counter',
      code: `<div className="flex items-center gap-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-4 border border-yellow-500/30">
  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center">
    <Sparkles className="w-5 h-5 text-white" />
  </div>
  <div>
    <p className="text-sm text-gray-400">Total Points</p>
    <p className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
      2,540
    </p>
  </div>
</div>`
    },
    {
      name: 'Gaming Card',
      code: `<Card className="bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-xl border border-purple-500/30 rounded-2xl p-6 shadow-2xl shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300">
  <CardHeader>
    <CardTitle className="text-xl font-bold bg-gradient-to-r from-violet-400 to-pink-400 bg-clip-text text-transparent">
      Epic Gaming Card
    </CardTitle>
    <CardDescription className="text-purple-300">
      Beautiful gaming-themed card with neon effects
    </CardDescription>
  </CardHeader>
  <CardContent>
    <p className="text-gray-300">Your content here with gaming aesthetics</p>
  </CardContent>
</Card>`
    }
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className={`min-h-screen transition-all duration-300 ${
      darkMode 
        ? 'bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900' 
        : 'bg-gradient-to-br from-gray-50 via-purple-50 to-gray-50'
    }`}>
      {/* Enhanced UI Agent */}
      <EnhancedUIAgent theme="gaming" position="bottom-right" />

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-violet-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-4">
            🎨 UI Agent Showcase
          </h1>
          <p className={`text-xl mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Complete demonstration of enhanced UI agent capabilities
          </p>
          
          {/* Theme Toggle */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <Sun className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-yellow-500'}`} />
            <Switch
              checked={darkMode}
              onCheckedChange={setDarkMode}
              className="data-[state=checked]:bg-purple-600"
            />
            <Moon className={`w-5 h-5 ${darkMode ? 'text-purple-400' : 'text-gray-400'}`} />
            <span className={`ml-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {darkMode ? 'Dark' : 'Light'} Mode
            </span>
          </div>
        </motion.div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="colors" className="w-full">
          <TabsList className={`grid w-full grid-cols-5 mb-8 ${
            darkMode 
              ? 'bg-slate-800/50 border border-purple-500/30' 
              : 'bg-white border border-gray-200'
          }`}>
            <TabsTrigger value="colors" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Palette className="w-4 h-4 mr-2" />
              Colors
            </TabsTrigger>
            <TabsTrigger value="components" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Layers className="w-4 h-4 mr-2" />
              Components
            </TabsTrigger>
            <TabsTrigger value="buttons" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Zap className="w-4 h-4 mr-2" />
              Buttons
            </TabsTrigger>
            <TabsTrigger value="cards" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Monitor className="w-4 h-4 mr-2" />
              Cards
            </TabsTrigger>
            <TabsTrigger value="interactive" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Settings className="w-4 h-4 mr-2" />
              Interactive
            </TabsTrigger>
          </TabsList>

          {/* Colors Tab */}
          <TabsContent value="colors">
            {Object.entries(colorPalettes).map(([key, palette]) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <Card className={`${
                  darkMode 
                    ? 'bg-slate-800/50 border-purple-500/30' 
                    : 'bg-white border-gray-200'
                } shadow-xl`}>
                  <CardHeader>
                    <CardTitle className={darkMode ? 'text-white' : 'text-gray-900'}>
                      {palette.name}
                    </CardTitle>
                    <CardDescription>
                      Complete color palette with usage guidelines
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                      {palette.colors.map((color, index) => (
                        <motion.div
                          key={index}
                          whileHover={{ scale: 1.05 }}
                          className="text-center"
                        >
                          <div
                            className="w-full h-20 rounded-lg mb-2 border-2 border-white/20 shadow-lg cursor-pointer"
                            style={{ backgroundColor: color.value }}
                            onClick={() => copyToClipboard(color.value)}
                          />
                          <p className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                            {color.name}
                          </p>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            {color.value}
                          </p>
                          <p className={`text-xs mt-1 ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                            {color.description}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* Text Variations */}
            <Card className={`${
              darkMode 
                ? 'bg-slate-800/50 border-purple-500/30' 
                : 'bg-white border-gray-200'
            } shadow-xl`}>
              <CardHeader>
                <CardTitle className={darkMode ? 'text-white' : 'text-gray-900'}>
                  Text Variations
                </CardTitle>
                <CardDescription>
                  Different text styles and gradients available
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {textVariations.map((text, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-slate-900/20">
                      <div>
                        <h3 className={text.className}>
                          {text.name} - Sample Text
                        </h3>
                        <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {text.name}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(text.className)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Components Tab */}
          <TabsContent value="components">
            <div className="grid gap-6">
              {componentShowcase.map((component, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className={`${
                    darkMode 
                      ? 'bg-slate-800/50 border-purple-500/30' 
                      : 'bg-white border-gray-200'
                  } shadow-xl`}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className={darkMode ? 'text-white' : 'text-gray-900'}>
                          {component.name}
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(component.code)}
                          >
                            <Copy className="w-4 h-4 mr-2" />
                            Copy
                          </Button>
                          <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                            <Play className="w-4 h-4 mr-2" />
                            Try It
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {/* Live Preview */}
                      <div className="mb-4 p-6 rounded-lg bg-gradient-to-br from-slate-900 to-purple-900/50 border border-purple-500/30">
                        <div className="flex items-center justify-center">
                          {/* Render based on component type */}
                          {component.name === 'Gaming Button' && (
                            <Button className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl shadow-lg shadow-purple-500/25 border border-purple-400/30 hover:shadow-purple-500/40 transition-all duration-300 animate-pulse-neon">
                              Gaming Action
                            </Button>
                          )}
                          {component.name === 'Points Counter' && (
                            <div className="flex items-center gap-3 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-4 border border-yellow-500/30">
                              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center">
                                <Sparkles className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <p className="text-sm text-gray-400">Total Points</p>
                                <p className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                                  2,540
                                </p>
                              </div>
                            </div>
                          )}
                          {component.name === 'Gaming Card' && (
                            <Card className="bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-xl border border-purple-500/30 rounded-2xl p-6 shadow-2xl shadow-purple-500/20 hover:shadow-purple-500/40 transition-all duration-300 max-w-sm">
                              <CardHeader>
                                <CardTitle className="text-xl font-bold bg-gradient-to-r from-violet-400 to-pink-400 bg-clip-text text-transparent">
                                  Epic Gaming Card
                                </CardTitle>
                                <CardDescription className="text-purple-300">
                                  Beautiful gaming-themed card
                                </CardDescription>
                              </CardHeader>
                              <CardContent>
                                <p className="text-gray-300">Gaming aesthetics</p>
                              </CardContent>
                            </Card>
                          )}
                        </div>
                      </div>

                      {/* Code */}
                      <div className="bg-slate-900 rounded-lg p-4 overflow-x-auto">
                        <pre className="text-sm text-gray-300">
                          <code>{component.code}</code>
                        </pre>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          {/* Buttons Tab */}
          <TabsContent value="buttons">
            <div className="grid gap-6">
              <Card className={`${
                darkMode 
                  ? 'bg-slate-800/50 border-purple-500/30' 
                  : 'bg-white border-gray-200'
              } shadow-xl`}>
                <CardHeader>
                  <CardTitle className={darkMode ? 'text-white' : 'text-gray-900'}>
                    Button Variations
                  </CardTitle>
                  <CardDescription>
                    All available button styles and themes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {buttonVariants.map((variant, index) => (
                      <motion.div
                        key={index}
                        whileHover={{ scale: 1.02 }}
                        className="text-center p-4 rounded-lg bg-slate-900/20"
                      >
                        <Button className={`${variant.className} mb-3 w-full`}>
                          {variant.name}
                        </Button>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {variant.name}
                        </p>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(variant.className)}
                          className="mt-2"
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          Copy Classes
                        </Button>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Interactive Button States */}
              <Card className={`${
                darkMode 
                  ? 'bg-slate-800/50 border-purple-500/30' 
                  : 'bg-white border-gray-200'
              } shadow-xl`}>
                <CardHeader>
                  <CardTitle className={darkMode ? 'text-white' : 'text-gray-900'}>
                    Interactive States
                  </CardTitle>
                  <CardDescription>
                    Button states and interactions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Button className="bg-gradient-to-r from-violet-500 to-purple-600 text-white">
                      Normal
                    </Button>
                    <Button className="bg-gradient-to-r from-violet-600 to-purple-700 text-white">
                      Hover
                    </Button>
                    <Button className="bg-gradient-to-r from-violet-500 to-purple-600 text-white opacity-75">
                      Active
                    </Button>
                    <Button disabled className="bg-gray-400 text-gray-700">
                      Disabled
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Cards Tab */}
          <TabsContent value="cards">
            <div className="grid gap-6">
              {cardVariants.map((variant, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className={`${variant.className} shadow-xl`}>
                    <CardHeader>
                      <CardTitle className={variant.textClass}>
                        {variant.name}
                      </CardTitle>
                      <CardDescription className={`${variant.textClass} opacity-75`}>
                        Example of {variant.name.toLowerCase()} styling
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className={`${variant.textClass} opacity-90 mb-4`}>
                        This is a sample card showcasing the {variant.name.toLowerCase()} theme. 
                        It demonstrates how content looks with this particular styling approach.
                      </p>
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          className={variant.name === 'Light Mode' 
                            ? 'bg-gray-900 text-white hover:bg-gray-800' 
                            : 'bg-white/20 text-white hover:bg-white/30'
                          }
                        >
                          Action
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => copyToClipboard(variant.className)}
                          className={variant.textClass}
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          {/* Interactive Tab */}
          <TabsContent value="interactive">
            <div className="grid gap-6">
              {/* Form Elements */}
              <Card className={`${
                darkMode 
                  ? 'bg-slate-800/50 border-purple-500/30' 
                  : 'bg-white border-gray-200'
              } shadow-xl`}>
                <CardHeader>
                  <CardTitle className={darkMode ? 'text-white' : 'text-gray-900'}>
                    Interactive Form Elements
                  </CardTitle>
                  <CardDescription>
                    Complete form component showcase
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Inputs */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className={darkMode ? 'text-white' : 'text-gray-900'}>
                        Gaming Input
                      </Label>
                      <Input 
                        placeholder="Enter text..." 
                        className="bg-slate-700/50 backdrop-blur-sm border-purple-500/30 text-white placeholder-purple-300/60 focus:border-purple-400/60"
                      />
                    </div>
                    <div>
                      <Label className={darkMode ? 'text-white' : 'text-gray-900'}>
                        Standard Input
                      </Label>
                      <Input placeholder="Standard input..." />
                    </div>
                  </div>

                  {/* Switches and Checkboxes */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        Switches
                      </h4>
                      <div className="flex items-center space-x-2">
                        <Switch id="gaming-mode" className="data-[state=checked]:bg-purple-600" />
                        <Label htmlFor="gaming-mode" className={darkMode ? 'text-white' : 'text-gray-900'}>
                          Gaming Mode
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch id="notifications" />
                        <Label htmlFor="notifications" className={darkMode ? 'text-white' : 'text-gray-900'}>
                          Notifications
                        </Label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        Checkboxes
                      </h4>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="terms" />
                        <Label htmlFor="terms" className={darkMode ? 'text-white' : 'text-gray-900'}>
                          Accept terms
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="newsletter" defaultChecked />
                        <Label htmlFor="newsletter" className={darkMode ? 'text-white' : 'text-gray-900'}>
                          Newsletter
                        </Label>
                      </div>
                    </div>
                  </div>

                  {/* Progress Bars */}
                  <div className="space-y-4">
                    <h4 className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      Progress Indicators
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                            Gaming Level
                          </span>
                          <span className="text-purple-400 font-semibold">75%</span>
                        </div>
                        <Progress value={75} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className={darkMode ? 'text-gray-300' : 'text-gray-600'}>
                            Points Progress
                          </span>
                          <span className="text-cyan-400 font-semibold">45%</span>
                        </div>
                        <Progress value={45} className="h-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Gaming Stats */}
              <Card className="bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-xl border border-purple-500/30 shadow-2xl shadow-purple-500/20">
                <CardHeader>
                  <CardTitle className="text-white">Gaming Dashboard</CardTitle>
                  <CardDescription className="text-purple-300">
                    Live gaming statistics and achievements
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Stats Cards */}
                    {[
                      { icon: Star, label: 'Level', value: '42', color: 'from-yellow-400 to-orange-500' },
                      { icon: Award, label: 'Achievements', value: '28', color: 'from-purple-400 to-pink-500' },
                      { icon: TrendingUp, label: 'Rank', value: '#156', color: 'from-cyan-400 to-blue-500' }
                    ].map((stat, index) => (
                      <motion.div
                        key={index}
                        whileHover={{ scale: 1.05 }}
                        className="text-center p-4 rounded-xl bg-slate-800/50 border border-purple-500/20"
                      >
                        <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center mx-auto mb-3`}>
                          <stat.icon className="w-6 h-6 text-white" />
                        </div>
                        <p className="text-2xl font-bold text-white mb-1">{stat.value}</p>
                        <p className="text-sm text-gray-400">{stat.label}</p>
                      </motion.div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-6 flex flex-wrap gap-3 justify-center">
                    {[
                      { icon: Play, label: 'Start Game', color: 'from-green-500 to-emerald-600' },
                      { icon: Users, label: 'Join Party', color: 'from-blue-500 to-cyan-600' },
                      { icon: ShoppingCart, label: 'Shop', color: 'from-purple-500 to-pink-600' },
                      { icon: Bell, label: 'Notifications', color: 'from-orange-500 to-red-600' }
                    ].map((action, index) => (
                      <Button
                        key={index}
                        className={`bg-gradient-to-r ${action.color} hover:opacity-90 transition-all duration-300 shadow-lg`}
                      >
                        <action.icon className="w-4 h-4 mr-2" />
                        {action.label}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-16 pb-8"
        >
          <div className="flex items-center justify-center gap-4 mb-4">
            <Badge variant="outline" className="text-purple-400 border-purple-400">
              Enhanced UI Agent
            </Badge>
            <Badge variant="outline" className="text-cyan-400 border-cyan-400">
              Gaming Theme
            </Badge>
            <Badge variant="outline" className="text-pink-400 border-pink-400">
              Dark Mode Ready
            </Badge>
          </div>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            ✨ All components are responsive, accessible, and ready for production use
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default UIAgentShowcase;