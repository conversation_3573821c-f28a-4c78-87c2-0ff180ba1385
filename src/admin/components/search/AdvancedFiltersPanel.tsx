/**
 * Advanced Filters Panel Component
 *
 * Comprehensive filtering interface for advanced search functionality.
 * Provides dynamic filter creation, management, and saved presets.
 * Features Gaming/Tech Enthusiast design with enhanced user experience.
 *
 * Features:
 * - Dynamic filter creation
 * - Multiple filter operators
 * - Filter presets and templates
 * - Real-time filter validation
 * - Gaming-inspired UI design
 * - Mobile responsive
 *
 * @component
 * <AUTHOR> Team
 */

'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Filter,
  Plus,
  X,
  Save,
  Bookmark,
  Trash2,
  Calendar,
  Hash,
  Type,
  ToggleLeft,
  List,
  ChevronDown,
  Zap
} from 'lucide-react';
import { AdminButton, AdminCard, AdminModal } from '../common';
import { useAdvancedSearch } from '../../hooks/useAdvancedSearch';
import {
  SearchFilter,
  SearchOperator,
  SearchFieldType,
  SearchEntityType,
  SearchPreset,
  EntitySearchConfig
} from '../../types/search';

/**
 * Props for AdvancedFiltersPanel component
 */
interface AdvancedFiltersPanelProps {
  /** Whether panel is open */
  isOpen: boolean;
  /** Close panel handler */
  onClose: () => void;
  /** Entity type to filter (optional */
  entityType?: SearchEntityType;
}

/**
 * AdvancedFiltersPanel component for managing search filters
 */
const AdvancedFiltersPanel: React.FC<AdvancedFiltersPanelProps> = ({
  isOpen,
  onClose,
  entityType
}) => {
  const [showSavePresetModal, setShowSavePresetModal] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [presetDescription, setPresetDescription] = useState('');

  const {
    query,
    addFilter,
    updateFilter,
    removeFilter,
    clearFilters,
    entityConfigs,
    presets,
    saveAsPreset,
    loadPreset
  } = useAdvancedSearch();

  /**
   * Get available fields for current entity types
   */
  const getAvailableFields = () => {
    const fields: { key: string; label: string; type: SearchFieldType; config: EntitySearchConfig }[] = [];
    
    const entityTypes = entityType ? [entityType] : query.entityTypes.filter(t => t !== 'all');
    
    for (const type of entityTypes) {
      const config = entityConfigs[type];
      if (config) {
        for (const field of config.fields.filter(f => f.filterable)) {
          fields.push({
            key: `${type}.${field.key}`,
            label: `${config.label} - ${field.label}`,
            type: field.type,
            config: field.config
          });
        }
      }
    }
    
    return fields;
  };

  /**
   * Get operators for field type
   */
  const getOperatorsForType = (fieldType: SearchFieldType): { value: SearchOperator; label: string }[] => {
    const operatorMap: Record<SearchFieldType, { value: SearchOperator; label: string }[]> = {
      text: [
        { value: 'contains', label: 'Contains' },
        { value: 'not_contains', label: 'Does not contain' },
        { value: 'equals', label: 'Equals' },
        { value: 'not_equals', label: 'Does not equal' },
        { value: 'starts_with', label: 'Starts with' },
        { value: 'ends_with', label: 'Ends with' }
      ],
      number: [
        { value: 'equals', label: 'Equals' },
        { value: 'not_equals', label: 'Does not equal' },
        { value: 'greater_than', label: 'Greater than' },
        { value: 'less_than', label: 'Less than' },
        { value: 'greater_than_or_equal', label: 'Greater than or equal' },
        { value: 'less_than_or_equal', label: 'Less than or equal' },
        { value: 'between', label: 'Between' }
      ],
      date: [
        { value: 'equals', label: 'On date' },
        { value: 'greater_than', label: 'After' },
        { value: 'less_than', label: 'Before' },
        { value: 'between', label: 'Between dates' }
      ],
      boolean: [
        { value: 'equals', label: 'Is' }
      ],
      select: [
        { value: 'equals', label: 'Is' },
        { value: 'not_equals', label: 'Is not' },
        { value: 'in', label: 'Is any of' },
        { value: 'not_in', label: 'Is none of' }
      ],
      multiselect: [
        { value: 'in', label: 'Contains any' },
        { value: 'not_in', label: 'Contains none' }
      ],
      range: [
        { value: 'between', label: 'Between' }
      ],
      daterange: [
        { value: 'between', label: 'Between dates' }
      ]
    };

    return operatorMap[fieldType] || [];
  };

  /**
   * Get field type icon
   */
  const getFieldTypeIcon = (fieldType: SearchFieldType) => {
    switch (fieldType) {
      case 'text': return Type;
      case 'number': return Hash;
      case 'date': return Calendar;
      case 'boolean': return ToggleLeft;
      case 'select':
      case 'multiselect': return List;
      default: return Filter;
    }
  };

  /**
   * Add new filter
   */
  const handleAddFilter = () => {
    const availableFields = getAvailableFields();
    if (availableFields.length === 0) return;

    const firstField = availableFields[0];
    const operators = getOperatorsForType(firstField.type);
    
    addFilter({
      field: firstField.key,
      operator: operators[0]?.value || 'equals',
      value: '',
      fieldType: firstField.type,
      label: firstField.label
    });
  };

  /**
   * Save current filters as preset
   */
  const handleSavePreset = async () => {
    if (!presetName.trim()) return;
    
    await saveAsPreset(presetName, presetDescription);
    setShowSavePresetModal(false);
    setPresetName('');
    setPresetDescription('');
  };

  const availableFields = getAvailableFields();
  const activeFilters = query.filters.filter(f => f.enabled);

  if (!isOpen) return null;

  return (
    <>
      <motion.div
        initial={{ opacity: 0, x: 300 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 300 }}
        className="fixed right-0 top-0 h-full w-96 bg-gray-900 border-l border-gray-700 z-50 overflow-hidden flex flex-col"
      >
        {/* Header */}
        <header className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-white flex items-center">
              <Filter size={20} className="mr-2 text-accent-400" />
              Advanced Filters
            </h2>
            <AdminButton
              variant="ghost"
              size="sm"
              onClick={onClose}
              aria-label="Close panel"
            >
              ×
            </AdminButton>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 overflow-y-auto p-4 space-y-4">
          {/* Filter Presets */}
          {presets.length > 0 && (
            <AdminCard title="Filter Presets">
              <div className="space-y-2">
                {presets.map((preset) => (
                  <button
                    key={preset.id}
                    onClick={() => loadPreset(preset)}
                    className="w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors text-left"
                  >
                    <div>
                      <div className="text-white font-medium">{preset.name}</div>
                      {preset.description && (
                        <div className="text-gray-400 text-sm">{preset.description}</div>
                      )}
                    </div>
                    <Bookmark size={16} className="text-accent-400" />
                  </button>
                ))}
              </div>
            </AdminCard>
          )}

          {/* Active Filters */}
          <AdminCard title="Active Filters">
            <div className="flex justify-end space-x-2 mb-4">
              {activeFilters.length > 0 && (
                <>
                  <AdminButton
                    variant="ghost"
                    size="sm"
                    icon={Save}
                    onClick={() => setShowSavePresetModal(true)}
                  >
                    Save
                  </AdminButton>
                  <AdminButton
                    variant="ghost"
                    size="sm"
                    icon={Trash2}
                    onClick={clearFilters}
                  >
                    Clear
                  </AdminButton>
                </>
              )}
              <AdminButton
                variant="primary"
                size="sm"
                icon={Plus}
                onClick={handleAddFilter}
                disabled={availableFields.length === 0}
              >
                Add Filter
              </AdminButton>
            </div>
            {activeFilters.length === 0 ? (
              <div className="text-center py-8">
                <Filter size={48} className="mx-auto text-gray-500 mb-4" />
                <p className="text-gray-400">No active filters</p>
                <p className="text-gray-500 text-sm mt-1">
                  Add filters to refine your search results
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {activeFilters.map((filter) => (
                  <FilterRow
                    key={filter.id}
                    filter={filter}
                    availableFields={availableFields}
                    onUpdate={(updates) => updateFilter(filter.id, updates)}
                    onRemove={() => removeFilter(filter.id)}
                  />
                ))}
              </div>
            )}
          </AdminCard>

          {/* Filter Summary */}
          {activeFilters.length > 0 && (
            <AdminCard>
              <div className="text-sm text-gray-400">
                <div className="flex items-center justify-between">
                  <span>Active Filters:</span>
                  <span className="text-accent-400">{activeFilters.length}</span>
                </div>
                <div className="mt-2 space-y-1">
                  {activeFilters.map((filter) => (
                    <div key={filter.id} className="text-xs">
                      {filter.label || filter.field} {filter.operator} {String(filter.value)}
                    </div>
                  ))}
                </div>
              </div>
            </AdminCard>
          )}
        </main>
      </motion.div>

      {/* Save Preset Modal */}
      <AdminModal
        isOpen={showSavePresetModal}
        onClose={() => setShowSavePresetModal(false)}
        title="Save Filter Preset"
        footer={
          <div className="flex justify-end space-x-3">
            <AdminButton
              variant="secondary"
              onClick={() => setShowSavePresetModal(false)}
            >
              Cancel
            </AdminButton>
            <AdminButton
              variant="primary"
              onClick={handleSavePreset}
              disabled={!presetName.trim()}
            >
              Save Preset
            </AdminButton>
          </div>
        }
      >
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              Preset Name *
            </label>
            <input
              type="text"
              value={presetName}
              onChange={(e) => setPresetName(e.target.value)}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500"
              placeholder="Enter preset name"
            />
          </div>
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              Description
            </label>
            <textarea
              value={presetDescription}
              onChange={(e) => setPresetDescription(e.target.value)}
              rows={3}
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent-500 resize-none"
              placeholder="Optional description"
            />
          </div>
        </div>
      </AdminModal>
    </>
  );
};

/**
 * Individual Filter Row Component
 */
interface FilterRowProps {
  filter: SearchFilter;
  availableFields: { key: string; label: string; type: SearchFieldType; config: EntitySearchConfig }[];
  onUpdate: (updates: Partial<SearchFilter>) => void;
  onRemove: () => void;
}

const FilterRow: React.FC<FilterRowProps> = ({
  filter,
  availableFields,
  onUpdate,
  onRemove
}) => {
  const selectedField = availableFields.find(f => f.key === filter.field);
  const operators = selectedField ? getOperatorsForType(selectedField.type) : [];
  const FieldIcon = selectedField ? getFieldTypeIcon(selectedField.type) : Filter;

  /**
   * Handle field change
   */
  const handleFieldChange = (fieldKey: string) => {
    const field = availableFields.find(f => f.key === fieldKey);
    if (field) {
      const newOperators = getOperatorsForType(field.type);
      onUpdate({
        field: fieldKey,
        fieldType: field.type,
        operator: newOperators[0]?.value || 'equals',
        value: '',
        label: field.label
      });
    }
  };

  /**
   * Render value input based on field type
   */
  const renderValueInput = () => {
    switch (filter.fieldType) {
      case 'text':
        return (
          <input
            type="text"
            value={filter.value}
            onChange={(e) => onUpdate({ value: e.target.value })}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
            placeholder="Enter value"
          />
        );
      
      case 'number':
        return (
          <input
            type="number"
            value={filter.value}
            onChange={(e) => onUpdate({ value: parseFloat(e.target.value) || 0 })}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
            placeholder="Enter number"
          />
        )
      
      case 'date':
        return (
          <input
            type="date"
            value={filter.value}
            onChange={(e) => onUpdate({ value: e.target.value })}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
          />
        )
      
      case 'boolean':
        return (
          <select
            value={filter.value}
            onChange={(e) => onUpdate({ value: e.target.value === 'true' })}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        )
      
      default:
        return (
          <input
            type="text"
            value={filter.value}
            onChange={(e) => onUpdate({ value: e.target.value })}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
            placeholder="Enter value"
          />
        ;
    }
  };

  return (
    <div className="bg-gray-800/30 rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <FieldIcon size={16} className="text-accent-400 mr-2" />
          <span className="text-white font-medium text-sm">Filter</span>
        </div>
        <div className="flex items-center space-x-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filter.enabled}
              onChange={(e) => onUpdate({ enabled: e.target.checked })}
              className="w-4 h-4 text-accent-600 bg-gray-700 border-gray-600 rounded focus:ring-accent-500"
            />
            <span className="ml-2 text-gray-300 text-sm">Enabled</span>
          </label>
          <button
            onClick={onRemove}
            className="text-gray-400 hover:text-red-400 transition-colors"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {/* Field Selection */}
        <div>
          <label className="block text-gray-400 text-xs mb-1">Field</label>
          <select
            value={filter.field}
            onChange={(e) => handleFieldChange(e.target.value)}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
          >
            {availableFields.map((field) => (
              <option key={field.key} value={field.key}>
                {field.label}
              </option>
            ))}
          </select>
        </div>

        {/* Operator Selection */}
        <div>
          <label className="block text-gray-400 text-xs mb-1">Operator</label>
          <select
            value={filter.operator}
            onChange={(e) => onUpdate({ operator: e.target.value as SearchOperator })}
            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-accent-500"
          >
            {operators.map((op) => (
              <option key={op.value} value={op.value}>
                {op.label}
              </option>
            ))}
          </select>
        </div>

        {/* Value Input */}
        <div>
          <label className="block text-gray-400 text-xs mb-1">Value</label>
          {renderValueInput()}
        </div>
      </div>
    </div>
  );
};

/**
 * Get operators for field type (helper function)
 */
function getOperatorsForType(fieldType: SearchFieldType: { value: SearchOperator; label: string }[] {
  const operatorMap: Record<SearchFieldType, { value: SearchOperator; label: string }[]> = {
    text: [
      { value: 'contains', label: 'Contains' },
      { value: 'not_contains', label: 'Does not contain' },
      { value: 'equals', label: 'Equals' },
      { value: 'not_equals', label: 'Does not equal' },
      { value: 'starts_with', label: 'Starts with' },
      { value: 'ends_with', label: 'Ends with' }
    ],
    number: [
      { value: 'equals', label: 'Equals' },
      { value: 'not_equals', label: 'Does not equal' },
      { value: 'greater_than', label: 'Greater than' },
      { value: 'less_than', label: 'Less than' },
      { value: 'greater_than_or_equal', label: 'Greater than or equal' },
      { value: 'less_than_or_equal', label: 'Less than or equal' },
      { value: 'between', label: 'Between' }
    ],
    date: [
      { value: 'equals', label: 'On date' },
      { value: 'greater_than', label: 'After' },
      { value: 'less_than', label: 'Before' },
      { value: 'between', label: 'Between dates' }
    ],
    boolean: [
      { value: 'equals', label: 'Is' }
    ],
    select: [
      { value: 'equals', label: 'Is' },
      { value: 'not_equals', label: 'Is not' },
      { value: 'in', label: 'Is any of' },
      { value: 'not_in', label: 'Is none of' }
    ],
    multiselect: [
      { value: 'in', label: 'Contains any' },
      { value: 'not_in', label: 'Contains none' }
    ],
    range: [
      { value: 'between', label: 'Between' }
    ],
    daterange: [
      { value: 'between', label: 'Between dates' }
    ]
  };

  return operatorMap[fieldType] || [];
}

/**)
 * Get field type icon (helper function)
 */
function getFieldTypeIcon(fieldType: SearchFieldType) {
  switch (fieldType {
    case 'text': return Type;
    case 'number': return Hash;
    case 'date': return Calendar;
    case 'boolean': return ToggleLeft;
    case 'select':
    case 'multiselect': return List;
    default: return Filter;
  }
}

export default AdvancedFiltersPanel;
