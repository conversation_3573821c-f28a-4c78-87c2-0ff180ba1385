/**
 * Admin Confirmation Dialog Component
 *
 * Enhanced confirmation dialog with Gaming/Tech Enthusiast design approach.
 * Provides consistent confirmation UI for destructive actions with proper
 * accessibility and visual feedback.
 *
 * Features:
 * - Multiple variants (danger, warning, info)
 * - Gaming-inspired animations
 * - Keyboard navigation support
 * - Accessibility compliance
 * - Customizable content and actions
 * - Tech-themed styling
 *
 * @component
 * @example
 * ```jsx
 * <AdminConfirmDialog
 *   isOpen={showConfirm}
 *   onClose={handleClose}
 *   onConfirm={handleDelete}
 *   title="Delete Product"
 *   message="Are you sure you want to delete this product?"
 *   variant="danger"
 * />
 * ```
 *
 * <AUTHOR> Team
 */

'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, Info, AlertCircle, X } from 'lucide-react';
import { AdminButton } from './AdminButton';

/**
 * Dialog variant types
 */
type DialogVariant = 'danger' | 'warning' | 'info';

/**
 * Props interface for AdminConfirmDialog component
 */
interface AdminConfirmDialogProps {
  /** Whether dialog is open */
  isOpen: boolean;
  /** Close handler */
  onClose: () => void;
  /** Confirm action handler */
  onConfirm: () => void;
  /** Dialog title */
  title: string;
  /** Dialog message */
  message: string;
  /** Dialog variant */
  variant?: DialogVariant;
  /** Confirm button text */
  confirmText?: string;
  /** Cancel button text */
  cancelText?: string;
  /** Whether confirm action is loading */
  loading?: boolean;
  /** Additional details */
  details?: string;
}

/**
 * Get variant-specific styling and icon
 */
const getVariantConfig = (variant: DialogVariant) => {
  const configs = {
    danger: {
      icon: AlertTriangle,
      iconColor: 'text-red-400',
      borderColor: 'border-red-500/30',
      bgGradient: 'from-red-950/20 to-gray-900',
      confirmVariant: 'danger' as const
    },
    warning: {
      icon: AlertCircle,
      iconColor: 'text-yellow-400',
      borderColor: 'border-yellow-500/30',
      bgGradient: 'from-yellow-950/20 to-gray-900',
      confirmVariant: 'primary' as const
    },
    info: {
      icon: Info,
      iconColor: 'text-blue-400',
      borderColor: 'border-blue-500/30',
      bgGradient: 'from-blue-950/20 to-gray-900',
      confirmVariant: 'primary' as const
    }
  };
  return configs[variant];
};

/**
 * AdminConfirmDialog component for confirmation actions
 */
const AdminConfirmDialog: React.FC<AdminConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  variant = 'danger',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  loading = false,
  details
}) => {
  const config = getVariantConfig(variant);
  const IconComponent = config.icon;

  /**
   * Handle confirm action
   */
  const handleConfirm = () => {
    onConfirm();
  };

  /**
   * Handle escape key
   */
  React.useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && !loading) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, loading, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm"
            onClick={!loading ? onClose : undefined}
          />
          
          {/* Dialog Container */}
          <div className="flex min-h-full items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              className="relative w-full max-w-md"
            >
              {/* Dialog Content */}
              <div className={`relative bg-gradient-to-br ${config.bgGradient} rounded-lg border ${config.borderColor} shadow-2xl`}>
                {/* Gaming-inspired border glow */}
                <div className={`absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-${variant}-500/10 to-transparent opacity-50`} />
                
                {/* Header */}
                <header className="relative flex items-center justify-between p-6 pb-4">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center mr-4`}>
                      <IconComponent size={20} className={config.iconColor} />
                    </div>
                    <h2 className="text-xl font-semibold text-white">
                      {title}
                    </h2>
                  </div>
                  
                  {!loading && (
                    <button
                      onClick={onClose}
                      className="text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-accent-500"
                      aria-label="Close dialog"
                    >
                      <X size={20} />
                    </button>
                  )}
                </header>
                
                {/* Body */}
                <main className="relative px-6 pb-6">
                  <p className="text-gray-300 mb-4">
                    {message}
                  </p>
                  
                  {details && (
                    <div className="bg-gray-800/30 rounded-lg p-3 mb-4">
                      <p className="text-gray-400 text-sm">
                        {details}
                      </p>
                    </div>
                  )}
                  
                  {/* Actions */}
                  <div className="flex justify-end space-x-3">
                    <AdminButton
                      variant="secondary"
                      onClick={onClose}
                      disabled={loading}
                    >
                      {cancelText}
                    </AdminButton>
                    <AdminButton
                      variant={config.confirmVariant}
                      onClick={handleConfirm}
                      loading={loading}
                    >
                      {confirmText}
                    </AdminButton>
                  </div>
                </main>
                
                {/* Tech-inspired corner accents */}
                <div className={`absolute top-0 left-0 w-4 h-4 border-l-2 border-t-2 ${config.borderColor} rounded-tl-lg`}></div>
                <div className={`absolute top-0 right-0 w-4 h-4 border-r-2 border-t-2 ${config.borderColor} rounded-tr-lg`}></div>
                <div className={`absolute bottom-0 left-0 w-4 h-4 border-l-2 border-b-2 ${config.borderColor} rounded-bl-lg`}></div>
                <div className={`absolute bottom-0 right-0 w-4 h-4 border-r-2 border-b-2 ${config.borderColor} rounded-br-lg`}></div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default AdminConfirmDialog;
