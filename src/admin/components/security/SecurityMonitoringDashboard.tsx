/**
 * Security Monitoring Dashboard Component
 * 
 * Comprehensive dashboard for monitoring admin security, permissions,
 * navigation patterns, and real-time validation events.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  Activity,
  Users,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  Eye,
  Lock,
  Zap,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { useRealTimePermissions, usePermissionEvents } from '../../hooks/useRealTimePermissions';
import { useNavigation } from '../../contexts/NavigationContext';
import { realTimePermissionService } from '../../services/realTimePermissionService';
import AdminCard from '../common/AdminCard';
import AdminButton from '../common/AdminButton';

interface SecurityMonitoringDashboardProps {
  className?: string;
}

const SecurityMonitoringDashboard: React.FC<SecurityMonitoringDashboardProps> = ({
  className = ''
}) => {
  const {
    permissionEvents,
    validationStats,
    lastValidation,
    isValidating,
    refreshPermissions,
    clearEvents
  } = useRealTimePermissions();

  const { events: securityEvents } = usePermissionEvents(['permission_denied', 'session_expired']);
  const { getNavigationInsights } = useNavigation();
  
  const [serviceStats, setServiceStats] = useState({
    cacheSize: 0,
    subscriberCount: 0,
    queueSize: 0,
    cacheHitRate: 0
  });

  const [navigationInsights, setNavigationInsights] = useState<any>(null);

  // Update service stats periodically
  useEffect(() => {
    const updateStats = () => {
      setServiceStats(realTimePermissionService.getValidationStats());
      setNavigationInsights(getNavigationInsights());
    };

    updateStats();
    const interval = setInterval(updateStats, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  
    
    // Cleanup interval
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [getNavigationInsights]);

  const securityMetrics = [
    {
      title: 'Permission Checks',
      value: validationStats.totalChecks,
      change: '+12%',
      changeType: 'positive' as const,
      icon: Shield,
      color: 'blue'
    },
    {
      title: 'Successful Validations',
      value: validationStats.successfulChecks,
      change: `${validationStats.totalChecks > 0 ? Math.round((validationStats.successfulChecks / validationStats.totalChecks) * 100) : 0}%`,
      changeType: 'positive' as const,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: 'Access Denials',
      value: validationStats.failedChecks,
      change: securityEvents.length > 0 ? 'Recent activity' : 'No issues',
      changeType: securityEvents.length > 0 ? 'negative' as 'negative' : 'neutral' as 'neutral',
      icon: XCircle,
      color: 'red'
    },
    {
      title: 'Avg Response Time',
      value: `${Math.round()validationStats.averageResponseTime)}ms`,
      change: validationStats.averageResponseTime < 100 ? 'Excellent' : 'Good',
      changeType: 'positive' as const,
      icon: Zap,
      color: 'purple'
    }
  ];

  const recentEvents = permissionEvents.slice(0, 10);

  return (
    <div className={`security-monitoring-dashboard ${className}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="w-8 h-8 text-blue-400" />
          <div>
            <h2 className="text-2xl font-bold text-white">Security Monitoring</h2>
            <p className="text-gray-400">Real-time permission validation and security insights</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <AdminButton
            variant="secondary"
            icon={RefreshCw}
            onClick={refreshPermissions}
            loading={isValidating}
          >
            Refresh Permissions
          </AdminButton>
          
          <AdminButton
            variant="ghost"
            icon={Activity}
            onClick={clearEvents}
          >
            Clear Events
          </AdminButton>
        </div>
      </div>

      {/* Security Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {securityMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <motion.div
              key={metric.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <AdminCard className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm font-medium">{metric.title}</p>
                    <p className="text-2xl font-bold text-white mt-1">{metric.value}</p>
                    <p className={`text-xs mt-1 ${
                      metric.changeType === 'positive' ? 'text-green-400' :
                      metric.changeType === 'negative' ? 'text-red-400' :
                      'text-gray-400'
                    }`}>
                      {metric.change}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg bg-${metric.color}-600/20`}>
                    <IconComponent className={`w-6 h-6 text-${metric.color}-400`} />
                  </div>
                </div>
              </AdminCard>
            </motion.div>
          );
        })}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Recent Permission Events */}
        <AdminCard className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Recent Permission Events</h3>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isValidating ? 'bg-green-400 animate-pulse' : 'bg-gray-600'}`} />
              <span className="text-xs text-gray-400">
                {isValidating ? 'Validating...' : 'Idle'}
              </span>
            </div>
          </div>

          <div className="space-y-3 max-h-80 overflow-y-auto">
            {recentEvents.length > 0 ? (
              recentEvents.map((event, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg"
                >
                  <div className={`p-2 rounded-full ${
                    event.type === 'permission_granted' ? 'bg-green-600/20' :
                    event.type === 'permission_denied' ? 'bg-red-600/20' :
                    event.type === 'permission_updated' ? 'bg-blue-600/20' :
                    'bg-yellow-600/20'
                  }`}>
                    {event.type === 'permission_granted' && <CheckCircle className="w-4 h-4 text-green-400" />}
                    {event.type === 'permission_denied' && <XCircle className="w-4 h-4 text-red-400" />}
                    {event.type === 'permission_updated' && <RefreshCw className="w-4 h-4 text-blue-400" />}
                    {event.type === 'session_expired' && <AlertTriangle className="w-4 h-4 text-yellow-400" />}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-white capitalize">
                      {event.type.replace()'_', ' ')}
                    </p>
                    <p className="text-xs text-gray-400 truncate">
                      Admin: {event.adminId} • {event.resource && `${event.resource}:${event.action}`}
                    </p>
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Activity className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">No recent permission events</p>
              </div>
            )}
          </div>
        </AdminCard>

        {/* Service Statistics */}
        <AdminCard className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Service Statistics</h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-gray-300">Active Subscribers</span>
              </div>
              <span className="text-sm font-medium text-white">{serviceStats.subscriberCount}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <BarChart3 className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-300">Cache Size</span>
              </div>
              <span className="text-sm font-medium text-white">{serviceStats.cacheSize}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-purple-400" />
                <span className="text-sm text-gray-300">Queue Size</span>
              </div>
              <span className="text-sm font-medium text-white">{serviceStats.queueSize}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-orange-400" />
                <span className="text-sm text-gray-300">Cache Hit Rate</span>
              </div>
              <span className="text-sm font-medium text-white">
                {Math.round(serviceStats.cacheHitRate * 100)}%
              </span>
            </div>
          </div>

          {lastValidation && (
            <div className="mt-6 pt-4 border-t border-gray-700">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-xs text-gray-400">
                  Last validation: {lastValidation.toLocaleTimeString()}
                </span>
              </div>
            </div>
          )}
        </AdminCard>
      </div>

      {/* Navigation Insights */}
      {navigationInsights && (
        <AdminCard className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Navigation Insights</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{navigationInsights.totalNavigations}</p>
              <p className="text-sm text-gray-400">Total Navigations</p>
            </div>
            
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{navigationInsights.uniqueAdmins}</p>
              <p className="text-sm text-gray-400">Unique Admins</p>
            </div>
            
            <div className="text-center">
              <p className="text-2xl font-bold text-red-400">
                {Math.round((navigationInsights.permissionDenialRate || 0) * 100)}%
              </p>
              <p className="text-sm text-gray-400">Denial Rate</p>
            </div>
          </div>
        </AdminCard>
      )}
    </div>
  );
};

export default SecurityMonitoringDashboard;
