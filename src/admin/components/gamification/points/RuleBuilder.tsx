/**
 * Rule Builder Component
 * 
 * Comprehensive interface for creating and editing gamification rules
 * with drag-and-drop conditions, visual configuration, and preview functionality.
 * 
 * <AUTHOR> Team
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Plus,
  X,
  Save,
  Eye,
  Settings,
  Calendar,
  Target,
  Zap,
  AlertCircle
} from 'lucide-react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import AdminButton from '../../common/AdminButton'
import AdminCard from '../../common/AdminCard'
import AdminModal from '../../common/AdminModal'
import type { GamificationRule, RuleCondition, RuleAction } from '../../../../lib/firestore'

interface RuleBuilderProps {
  isOpen: boolean
  onClose: () => void
  onSave: (rule: Partial<GamificationRule>) => void
  initialRule?: GamificationRule | null
  mode: 'create' | 'edit'
}

interface RulePreview {
  estimatedImpact: string
  affectedUsers: number
  pointsPerExecution: number
  executionsPerDay: number
}

export const RuleBuilder: React.FC<RuleBuilderProps> = ({
  isOpen,
  onClose,
  onSave,
  initialRule,
  mode
}) => {
  // ===== STATE =====
  
  const [ruleData, setRuleData] = useState<Partial<GamificationRule>>({
    type: 'points',
    name: '',
    description: '',
    conditions: [],
    actions: [],
    isActive: true,
    priority: 1
  })
  
  const [showPreview, setShowPreview] = useState(false)
  const [preview, setPreview] = useState<RulePreview | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  // ===== EFFECTS =====

  useEffect(() => {
    if (initialRule && mode === 'edit') {
      setRuleData({
        type: initialRule.type,
        name: initialRule.name,
        description: initialRule.description,
        conditions: initialRule.conditions,
        actions: initialRule.actions,
        isActive: initialRule.isActive,
        priority: initialRule.priority
      })
    } else {
      // Reset for create mode
      setRuleData({
        type: 'points',
        name: '',
        description: '',
        conditions: [],
        actions: [],
        isActive: true,
        priority: 1
      })
    }
    setErrors({})
  }, [initialRule, mode, isOpen])

  // ===== HANDLERS =====

  const handleAddCondition = () => {
    const newCondition: RuleCondition = {
      type: 'purchase_amount',
      operator: 'greater_than',
      value: 0
    }
    
    setRuleData(prev => ({
      ...prev,
      conditions: [...(prev.conditions || []), newCondition]
    }))
  }

  const handleUpdateCondition = (index: number, condition: RuleCondition) => {
    setRuleData(prev => ({
      ...prev,
      conditions: prev.conditions?.map((c, i) => i === index ? condition : c) || []
    }))
  }

  const handleRemoveCondition = (index: number) => {
    setRuleData(prev => ({
      ...prev,
      conditions: prev.conditions?.filter((_, i) => i !== index) || []
    }))
  }

  const handleAddAction = () => {
    const newAction: RuleAction = {
      type: 'award_points',
      value: 50
    }
    
    setRuleData(prev => ({
      ...prev,
      actions: [...(prev.actions || []), newAction]
    }))
  }

  const handleUpdateAction = (index: number, action: RuleAction) => {
    setRuleData(prev => ({
      ...prev,
      actions: prev.actions?.map((a, i) => i === index ? action : a) || []
    }))
  }

  const handleRemoveAction = (index: number) => {
    setRuleData(prev => ({
      ...prev,
      actions: prev.actions?.filter((_, i) => i !== index) || []
    }))
  }

  const validateRule = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!ruleData.name?.trim()) {
      newErrors.name = 'Rule name is required'
    }

    if (!ruleData.description?.trim()) {
      newErrors.description = 'Rule description is required'
    }

    if (!ruleData.conditions?.length) {
      newErrors.conditions = 'At least one condition is required'
    }

    if (!ruleData.actions?.length) {
      newErrors.actions = 'At least one action is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const generatePreview = (): RulePreview => {
    const pointsPerAction = ruleData.actions?.reduce((sum, action) => {
      if (action.type === 'award_points') {
        return sum + (typeof action.value === 'number' ? action.value : 0)
      }
      return sum
    }, 0) || 0

    return {
      estimatedImpact: pointsPerAction > 50 ? 'High' : pointsPerAction > 20 ? 'Medium' : 'Low',
      affectedUsers: Math.floor(Math.random() * 1000 + 100), // Mock calculation
      pointsPerExecution: pointsPerAction,
      executionsPerDay: Math.floor(Math.random() * 50 + 10) // Mock calculation
    }
  }

  const handlePreview = () => {
    if (validateRule()) {
      setPreview(generatePreview())
      setShowPreview(true)
    }
  }

  const handleSave = async () => {
    if (!validateRule()) return

    setLoading(true)
    try {
      await onSave(ruleData)
      onClose()
    } catch (error) {
      console.error('Error saving rule:', error)
    } finally {
      setLoading(false)
    }
  }

  // ===== RENDER =====

  return (
    <AdminModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'edit' ? 'Edit Gamification Rule' : 'Create New Gamification Rule'}
      size="xl"
    >
      <div className="space-y-6">
        {/* Basic Information */}
        <AdminCard title="Basic Information" className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Rule Type
              </label>
              <select
                value={ruleData.type}
                onChange={(e) => setRuleData(prev => ({ ...prev, type: e.target.value as any }))}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
              >
                <option value="points">Points Award</option>
                <option value="achievement">Achievement</option>
                <option value="reward">Reward</option>
                <option value="notification">Notification</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Priority Level
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={ruleData.priority}
                onChange={(e) => setRuleData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-accent-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Rule Name
            </label>
            <input
              type="text"
              value={ruleData.name}
              onChange={(e) => setRuleData(prev => ({ ...prev, name: e.target.value }))}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:border-accent-500 ${
                errors.name ? 'border-red-500' : 'border-gray-700'
              }`}
              placeholder="Enter rule name..."
            />
            {errors.name && (
              <p className="text-red-400 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={ruleData.description}
              onChange={(e) => setRuleData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:border-accent-500 ${
                errors.description ? 'border-red-500' : 'border-gray-700'
              }`}
              placeholder="Describe what this rule does..."
            />
            {errors.description && (
              <p className="text-red-400 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          <div className="mt-4 flex items-center">
            <input
              type="checkbox"
              id="isActive"
              checked={ruleData.isActive}
              onChange={(e) => setRuleData(prev => ({ ...prev, isActive: e.target.checked }))}
              className="w-4 h-4 text-accent-600 bg-gray-800 border-gray-600 rounded focus:ring-accent-500"
            />
            <label htmlFor="isActive" className="ml-2 text-sm text-gray-300">
              Activate rule immediately after saving
            </label>
          </div>
        </AdminCard>

        {/* Conditions Configuration */}
        <AdminCard title="Rule Conditions" description="Define when this rule should trigger" className="p-6">
          <div className="space-y-4">
            {ruleData.conditions?.map((condition, index) => (
              <div key={index} className="p-4 bg-gray-800 rounded-lg border border-gray-700">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-medium">Condition {index + 1}</h4>
                  <button
                    onClick={() => handleRemoveCondition(index)}
                    className="p-1 text-red-400 hover:text-red-300 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Type</label>
                    <select
                      value={condition.type}
                      onChange={(e) => handleUpdateCondition(index, { ...condition, type: e.target.value as any })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-accent-500"
                    >
                      <option value="user_tier">User Tier</option>
                      <option value="purchase_amount">Purchase Amount</option>
                      <option value="time_range">Time Range</option>
                      <option value="user_segment">User Segment</option>
                      <option value="custom">Custom</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Operator</label>
                    <select
                      value={condition.operator}
                      onChange={(e) => handleUpdateCondition(index, { ...condition, operator: e.target.value as any })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-accent-500"
                    >
                      <option value="equals">Equals</option>
                      <option value="greater_than">Greater Than</option>
                      <option value="less_than">Less Than</option>
                      <option value="contains">Contains</option>
                      <option value="in_range">In Range</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Value</label>
                    <input
                      type="text"
                      value={condition.value}
                      onChange={(e) => handleUpdateCondition(index, { ...condition, value: e.target.value })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-accent-500"
                      placeholder="Enter value..."
                    />
                  </div>
                </div>
              </div>
            ))

            <AdminButton
              variant="secondary"
              icon={Plus}
              onClick={handleAddCondition}
              className="w-full"
            >
              Add Condition
            </AdminButton>

            {errors.conditions && (
              <p className="text-red-400 text-sm">{errors.conditions}</p>
            )}
          </div>
        </AdminCard>

        {/* Actions Configuration */}
        <AdminCard title="Rule Actions" description="Define what happens when conditions are met" className="p-6">
          <div className="space-y-4">
            {ruleData.actions?.map((action, index) => (
              <div key={index} className="p-4 bg-gray-800 rounded-lg border border-gray-700">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-medium">Action {index + 1}</h4>
                  <button
                    onClick={() => handleRemoveAction(index)}
                    className="p-1 text-red-400 hover:text-red-300 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Action Type</label>
                    <select
                      value={action.type}
                      onChange={(e) => handleUpdateAction(index, { ...action, type: e.target.value as any })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-accent-500"
                    >
                      <option value="award_points">Award Points</option>
                      <option value="multiply_points">Multiply Points</option>
                      <option value="award_achievement">Award Achievement</option>
                      <option value="send_notification">Send Notification</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      {action.type === 'award_points' || action.type === 'multiply_points' ? 'Points/Multiplier' : 'Value'}
                    </label>
                    <input
                      type={action.type === 'award_points' || action.type === 'multiply_points' ? 'number' : 'text'}
                      value={action.value}
                      onChange={(e) => handleUpdateAction(index, {
                        ...action,
                        value: (action.type === 'award_points' || action.type === 'multiply_points')
                          ? parseInt(e.target.value) || 0
                          : e.target.value
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-accent-500"
                      placeholder={action.type === 'award_points' ? 'Enter points...' : 'Enter value...'}
                    />
                  </div>
                </div>
              </div>
            ))}

            <AdminButton
              variant="secondary"
              icon={Plus}
              onClick={handleAddAction}
              className="w-full"
            >
              Add Action
            </AdminButton>

            {errors.actions && (
              <p className="text-red-400 text-sm">{errors.actions}</p>
            )}
          </div>
        </AdminCard>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <div className="flex space-x-3">
            <AdminButton
              variant="secondary"
              icon={Eye}
              onClick={handlePreview}
              disabled={!ruleData.name || !ruleData.description}
            >
              Preview Impact
            </AdminButton>
          </div>

          <div className="flex space-x-3">
            <AdminButton
              variant="secondary"
              onClick={onClose}
            >
              Cancel
            </AdminButton>
            <AdminButton
              variant="primary"
              icon={Save}
              onClick={handleSave}
              loading={loading}
            >
              {mode === 'edit' ? 'Update Rule' : 'Create Rule'}
            </AdminButton>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      <AdminModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        title="Rule Impact Preview"
        size="md"
      >
        {preview && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-800 rounded-lg">
                <p className="text-gray-400 text-sm">Estimated Impact</p>
                <p className="text-white font-bold text-lg">{preview.estimatedImpact}</p>
              </div>
              <div className="p-4 bg-gray-800 rounded-lg">
                <p className="text-gray-400 text-sm">Affected Users</p>
                <p className="text-white font-bold text-lg">{preview.affectedUsers.toLocaleString()}</p>
              </div>
              <div className="p-4 bg-gray-800 rounded-lg">
                <p className="text-gray-400 text-sm">Points per Execution</p>
                <p className="text-white font-bold text-lg">{preview.pointsPerExecution}</p>
              </div>
              <div className="p-4 bg-gray-800 rounded-lg">
                <p className="text-gray-400 text-sm">Est. Executions/Day</p>
                <p className="text-white font-bold text-lg">{preview.executionsPerDay}</p>
              </div>
            </div>
            
            <div className="flex justify-end">
              <AdminButton
                variant="secondary"
                onClick={() => setShowPreview(false)}
              >
                Close Preview
              </AdminButton>
            </div>
          </div>
        )}
      </AdminModal>
    </AdminModal>
  )
}

export default RuleBuilder
