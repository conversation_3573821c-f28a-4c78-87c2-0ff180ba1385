/**
 * Global Search Bar Component
 *
 * Advanced search interface with real-time suggestions, entity filtering,
 * and quick access to search functionality. Features Gaming/Tech Enthusiast
 * design with enhanced user experience.
 *
 * Features:
 * - Real-time search with suggestions
 * - Entity type filtering
 * - Recent searches
 * - Keyboard navigation
 * - Gaming-inspired UI design
 * - Mobile responsive
 *
 * @component
 * <AUTHOR> Team
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Filter,
  Clock,
  TrendingUp,
  Package,
  Users,
  ShoppingCart,
  X,
  ChevronDown,
  Zap
} from 'lucide-react';
import { useAdvancedSearch, useSearchSuggestions } from '../../hooks/useAdvancedSearch';
import { SearchEntityType, SearchSuggestion } from '../../types/search';

/**
 * Props for GlobalSearchBar component
 */
interface GlobalSearchBarProps {
  /** Placeholder text */
  placeholder?: string;
  /** Whether to show entity filter */
  showEntityFilter?: boolean;
  /** Whether to show advanced filters button */
  showAdvancedFilters?: boolean;
  /** Callback when advanced filters is clicked */
  onAdvancedFilters?: () => void;
  /** Callback when search result is selected */
  onResultSelect?: (resultId: string, entityType: SearchEntityType) => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * GlobalSearchBar component for advanced search functionality
 */
const GlobalSearchBar: React.FC<GlobalSearchBarProps> = ({
  placeholder = "Search products, users, orders...",
  showEntityFilter = true,
  showAdvancedFilters = true,
  onAdvancedFilters,
  onResultSelect,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedEntityType, setSelectedEntityType] = useState<SearchEntityType>('all');
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    query,
    results,
    loading,
    setSearchTerm,
    setEntityTypes,
    recentSearches,
    entityConfigs
  } = useAdvancedSearch({
    autoSearch: true,
    debounceDelay: 300
  });

  const { suggestions } = useSearchSuggestions(query.term);

  /**
   * Handle click outside to close dropdown
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  /**
   * Handle search input change
   */
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setIsOpen(value.length > 0);
  };

  /**
   * Handle entity type change
   */
  const handleEntityTypeChange = (entityType: SearchEntityType) => {
    setSelectedEntityType(entityType);
    setEntityTypes([entityType]);
  };

  /**
   * Handle suggestion selection
   */
  const handleSuggestionSelect = (suggestion: SearchSuggestion) => {
    setSearchTerm(suggestion.text);
    setIsOpen(false);
    inputRef.current?.blur();
  };

  /**
   * Handle recent search selection
   */
  const handleRecentSearchSelect = (searchTerm: string) => {
    setSearchTerm(searchTerm);
    setIsOpen(false);
    inputRef.current?.blur();
  };

  /**
   * Handle result selection
   */
  const handleResultSelect = (resultId: string, entityType: SearchEntityType) => {
    setIsOpen(false);
    inputRef.current?.blur();
    if (onResultSelect) {
      onResultSelect(resultId, entityType);
    }
  };

  /**
   * Get entity icon
   */
  const getEntityIcon = (entityType: SearchEntityType) => {
    switch (entityType {
      case 'products': return Package;
      case 'users': return Users;
      case 'orders': return ShoppingCart;
      default: return Search;
    }
  };

  /**
   * Get entity label
   */
  const getEntityLabel = (entityType: SearchEntityType) => {
    if (entityType === 'all' return 'All';
    return entityConfigs[entityType]?.label || entityType;
  };

  const hasContent = query.term.length > 0;
  const showResults = hasContent && results && results.items.length > 0;
  const showSuggestions = hasContent && suggestions.length > 0;
  const showRecentSearches = !hasContent && recentSearches.length > 0;

  return (
    <div ref=) {searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="flex items-center bg-gray-800/50 border border-gray-600/50 rounded-lg focus-within:ring-2 focus-within:ring-accent-500 focus-within:border-accent-500 transition-all">
          {/* Entity Filter */}
          {showEntityFilter && (
            <div className="flex items-center border-r border-gray-600/50 px-3">
              <select
                value={selectedEntityType}
                onChange={(e) => handleEntityTypeChange(e.target.value as SearchEntityType)}
                className="bg-transparent text-gray-300 text-sm focus:outline-none cursor-pointer"
              >
                <option value="all">All</option>
                {Object.entries(entityConfigs).map(([type, config]) => (
                  <option key={type} value={type}>
                    {config.label}
                  </option>
                ))}
              </select>
              <ChevronDown size={14} className="text-gray-400 ml-1" />
            </div>
          )}

          {/* Search Icon */}
          <div className="flex items-center pl-4">
            <Search size={20} className="text-gray-400" />
          </div>

          {/* Search Input */}
          <input
            ref={inputRef}
            type="text"
            value={query.term}
            onChange={(e) => handleSearchChange(e.target.value)}
            onFocus={() => setIsOpen(true)}
            placeholder={placeholder}
            className="flex-1 bg-transparent text-white placeholder-gray-400 px-4 py-3 focus:outline-none"
          />

          {/* Loading Indicator */}
          {loading && (
            <div className="px-3">
              <div className="w-4 h-4 border-2 border-accent-500 border-t-transparent rounded-full animate-spin" />
            </div>
          )}

          {/* Clear Button */}
          {hasContent && (
            <button
              onClick={() => {
                setSearchTerm('');
                setIsOpen(false);
              }}
              className="px-3 text-gray-400 hover:text-white transition-colors"
            >
              <X size={16} />
            </button>
          )}

          {/* Advanced Filters Button */}
          {showAdvancedFilters && (
            <button
              onClick={onAdvancedFilters}
              className="px-3 py-3 text-gray-400 hover:text-white transition-colors border-l border-gray-600/50"
              title="Advanced Filters"
            >
              <Filter size={16} />
            </button>
          )}
        </div>
      </div>

      {/* Search Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden"
          >
            <div className="max-h-96 overflow-y-auto">
              {/* Search Results */}
              {showResults && (
                <div className="p-2">
                  <div className="flex items-center justify-between px-3 py-2 text-xs text-gray-400 uppercase tracking-wider">
                    <span>Results ({results.totalResults})</span>
                    <span>{results.executionTime}ms</span>
                  </div>
                  <div className="space-y-1">
                    {results.items.slice(0, 5).map((item) => {
                      const EntityIcon = getEntityIcon(item.entityType);
                      return (
                        <button
                          key={`${item.entityType}-${item.id}`}
                          onClick={() => handleResultSelect(item.id, item.entityType)}
                          className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-700/50 rounded-lg transition-colors group"
                        >
                          <div className="w-8 h-8 bg-accent-600/20 rounded-lg flex items-center justify-center mr-3 group-hover:bg-accent-600/30 transition-colors">
                            <EntityIcon size={16} className="text-accent-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-white font-medium truncate">
                              {item.title}
                            </div>
                            {item.description && (
                              <div className="text-gray-400 text-sm truncate">
                                {item.description}
                              </div>
                            )}
                          </div>
                          <div className="text-xs text-gray-500 ml-2">
                            {getEntityLabel(item.entityType)}
                          </div>
                        </button>
                      );
                    })}
                  </div>
                  {results.totalResults > 5 && (
                    <div className="px-3 py-2 text-center">
                      <button className="text-accent-400 hover:text-accent-300 text-sm transition-colors">
                        View all {results.totalResults} results
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Search Suggestions */}
              {showSuggestions && (
                <div className="p-2 border-t border-gray-700">
                  <div className="px-3 py-2 text-xs text-gray-400 uppercase tracking-wider">
                    Suggestions
                  </div>
                  <div className="space-y-1">
                    {suggestions.slice(0, 3).map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionSelect(suggestion)}
                        className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-700/50 rounded-lg transition-colors"
                      >
                        <TrendingUp size={16} className="text-gray-400 mr-3" />
                        <span className="text-gray-300">{suggestion.text}</span>
                        <span className="text-xs text-gray-500 ml-auto">
                          {suggestion.resultCount} results
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Searches */}
              {showRecentSearches && (
                <div className="p-2">
                  <div className="px-3 py-2 text-xs text-gray-400 uppercase tracking-wider">
                    Recent Searches
                  </div>
                  <div className="space-y-1">
                    {recentSearches.slice(0, 5).map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleRecentSearchSelect(search)}
                        className="w-full flex items-center px-3 py-2 text-left hover:bg-gray-700/50 rounded-lg transition-colors"
                      >
                        <Clock size={16} className="text-gray-400 mr-3" />
                        <span className="text-gray-300">{search}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* No Results */}
              {hasContent && !loading && !showResults && !showSuggestions && (
                <div className="p-6 text-center">
                  <Search size={48} className="mx-auto text-gray-500 mb-4" />
                  <p className="text-gray-400">No results found for "{query.term}"</p>
                  <p className="text-gray-500 text-sm mt-1">
                    Try adjusting your search terms or filters
                  </p>
                </div>
              )}

              {/* Empty State */}
              {!hasContent && !showRecentSearches && (
                <div className="p-6 text-center">
                  <Zap size={48} className="mx-auto text-accent-500 mb-4" />
                  <p className="text-gray-400">Start typing to search</p>
                  <p className="text-gray-500 text-sm mt-1">
                    Search across products, users, orders, and more
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GlobalSearchBar;
export { GlobalSearchBar };
