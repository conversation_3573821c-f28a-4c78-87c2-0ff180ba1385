/**
 * Accessible Global Error Handler
 *
 * Enhanced global error component with comprehensive accessibility features.
 * Catches React rendering errors and provides accessible error recovery.
 *
 * Features:
 * - Full WCAG 2.1 AA compliance
 * - Screen reader announcements
 * - Keyboard navigation support
 * - ARIA landmarks and labels
 * - Focus management
 * - Error recovery guidance
 * - Sentry error reporting
 *
 * <AUTHOR> Team
 */

'use client';

import * as Sentry from '@sentry/nextjs';
import { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Home, Mail, Bug } from 'lucide-react';
import { AccessibilityUtils } from '@/lib/accessibility/accessibilityUtils';

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function AccessibleGlobalError({ error, reset }: GlobalErrorProps) {
  const mainRef = useRef<HTMLElement>(null)
  const headingRef = useRef<HTMLHeadingElement>(null)
  const errorId = `global-error-${Date.now()}`

  useEffect(() => {
    // Report error to Sentry
    Sentry.withScope((scope) => {
      scope.setTag('errorBoundary', 'global');
      scope.setLevel('fatal');
      scope.setContext('globalError', {
        digest: error.digest,
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        errorId
      });

      Sentry.captureException(error);
    });

    // Announce error to screen readers
    const announceError = () => {
      AccessibilityUtils.announce(
        `Critical application error occurred: ${error.message}. Error recovery options are available.`,
        { priority: 'high', delay: 500 })
      )
    }

    // Focus the main heading for screen readers
    const focusHeading = () => {
      if (headingRef.current) {
        headingRef.current.focus()
      }
    }

    // Set page title for screen readers
    document.title = 'Application Error - Syndicaps'

    // Execute accessibility enhancements
    announceError()
    setTimeout(focusHeading, 600)

    // Keyboard shortcut handler
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'r':
        case 'R':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            reset(
          }
          break
        case 'h':
        case 'H':)
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            window.location.href = '/'
          }
          break
        case 'Escape':
          // Focus the main content
          if (mainRef.current) {
            mainRef.current.focus()
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [error, reset]);

  return (
    <html lang="en">
      <head>
        <title>Application Error - Syndicaps</title>
        <meta name="description" content="An application error occurred. Error recovery options are available." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="bg-gray-950 text-white">
        {/* Skip Link for Screen Readers */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-purple-600 text-white px-4 py-2 rounded-lg z-50"
        >
          Skip to main content
        </a>

        <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
          <main
            ref={mainRef}
            id="main-content"
            className="max-w-2xl w-full text-center"
            role="main"
            aria-labelledby="error-heading"
            aria-describedby="error-description"
          >
            {/* Error Alert */}
            <div
              className="mb-8"
              role="alert"
              aria-live="assertive"
              aria-atomic="true"
              aria-labelledby={errorId}
            >
              <div className="mb-6">
                <div className="w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <AlertCircle className="w-10 h-10 text-red-600 dark:text-red-400" aria-hidden="true" />
                </div>

                <h1
                  ref={headingRef}
                  id="error-heading"
                  className="text-3xl font-bold text-white mb-4 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950 rounded-lg px-2 py-1"
                  tabIndex={-1}
                >
                  Application Error
                </h1>

                <p
                  id="error-description"
                  className="text-gray-300 text-lg leading-relaxed mb-4"
                >
                  We're sorry, but something unexpected happened. Our team has been automatically notified and is working to resolve the issue.
                </p>

                {/* Error Details for Development */}
                {process.env.NODE_ENV === 'development' && (
                  <details className="mt-4 text-left bg-gray-900 rounded-lg p-4">
                    <summary className="cursor-pointer text-sm text-gray-400 hover:text-gray-300 focus:text-gray-300 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950 rounded px-2 py-1">
                      Technical Details (Development Mode)
                    </summary>
                    <div className="mt-3 text-xs text-gray-500 space-y-2">
                      <div>
                        <strong className="text-gray-400">Error ID:</strong>
                        <span className="ml-2 font-mono">{errorId}</span>
                      </div>
                      <div>
                        <strong className="text-gray-400">Message:</strong>
                        <span className="ml-2">{error.message}</span>
                      </div>
                      {error.digest && (
                        <div>
                          <strong className="text-gray-400">Digest:</strong>
                          <span className="ml-2 font-mono">{error.digest}</span>
                        </div>
                      )}
                      {error.stack && (
                        <div>
                          <strong className="text-gray-400">Stack Trace:</strong>
                          <pre className="mt-1 text-xs bg-gray-800 p-2 rounded overflow-auto max-h-32">
                            {error.stack}
                          </pre>
                        </div>
                      )}
                    </div>
                  </details>
                )}
              </div>
            </div>

            {/* Error Recovery Actions */}
            <nav
              className="space-y-6"
              aria-labelledby="recovery-heading"
            >
              <h2
                id="recovery-heading"
                className="text-xl font-semibold text-white mb-4"
              >
                What you can do:
              </h2>

              {/* Primary Actions */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={reset}
                  className="inline-flex items-center gap-3 bg-purple-600 hover:bg-purple-700 focus:bg-purple-700 text-white px-8 py-4 rounded-lg font-medium transition-colors focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950 text-lg"
                  aria-describedby="retry-description"
                >
                  <RefreshCw className="w-5 h-5" aria-hidden="true" />
                  Try Again
                  <span className="text-sm opacity-75 ml-2">(Ctrl+R)</span>
                </Button>

                <Button
                  onClick={( => window.location.href = '/'}
                  variant="outline"
                  className="inline-flex items-center gap-3 bg-gray-800 hover:bg-gray-700 focus:bg-gray-700 text-white px-8 py-4 rounded-lg font-medium transition-colors focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-950 text-lg border-gray-600"
                  aria-describedby="home-description"
                >
                  <Home className="w-5 h-5" aria-hidden="true" />
                  Go Home
                  <span className="text-sm opacity-75 ml-2">(Ctrl+H)</span>
                </Button>
              </div>

              {/* Hidden descriptions for screen readers */}
              <div className="sr-only">
                <p id="retry-description">Retry the operation that caused this error</p>
                <p id="home-description">Return to the Syndicaps home page</p>
              </div>
            </nav>

            {/* Additional Help */}
            <section className="mt-8 pt-6 border-t border-gray-800">
              <h3 className="text-lg font-semibold text-white mb-4">
                Need more help?
              </h3>

              <div className="space-y-3">
                <p className="text-gray-300 text-sm">
                  If this error continues to occur, please contact our support team with the error ID:
                  <code className="bg-gray-800 px-2 py-1 rounded text-purple-400 font-mono text-xs ml-1">
                    {errorId}
                  </code>
                </p>

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-flex items-center gap-2 text-purple-400 hover:text-purple-300 focus:text-purple-300 underline hover:no-underline focus:no-underline focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950 rounded px-2 py-1"
                    aria-describedby="email-description"
                  >
                    <Mail className="w-4 h-4" aria-hidden="true" />
                    Email Support
                  </a>

                  <a
                    href="https://github.com/syndicaps/syndicaps/issues"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-purple-400 hover:text-purple-300 focus:text-purple-300 underline hover:no-underline focus:no-underline focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950 rounded px-2 py-1"
                    aria-describedby="github-description"
                  >
                    <Bug className="w-4 h-4" aria-hidden="true" />
                    Report Bug
                  </a>
                </div>

                {/* Hidden descriptions for screen readers */}
                <div className="sr-only">
                  <p id="email-description">Send an email to Syndicaps support team</p>
                  <p id="github-description">Report this bug on GitHub (opens in new tab)</p>
                </div>
              </div>
            </section>

            {/* Keyboard Shortcuts Help */}
            <details className="mt-6 text-left max-w-md mx-auto">
              <summary className="text-gray-400 cursor-pointer hover:text-gray-300 focus:text-gray-300 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-950 rounded px-2 py-1">
                Keyboard shortcuts available
              </summary>
              <div className="mt-3 text-sm text-gray-500 bg-gray-900 rounded-lg p-4">
                <ul className="space-y-1">
                  <li><kbd className="bg-gray-800 px-2 py-1 rounded text-xs">Ctrl+R</kbd> - Try again</li>
                  <li><kbd className="bg-gray-800 px-2 py-1 rounded text-xs">Ctrl+H</kbd> - Go to home page</li>
                  <li><kbd className="bg-gray-800 px-2 py-1 rounded text-xs">Escape</kbd> - Focus main content</li>
                  <li><kbd className="bg-gray-800 px-2 py-1 rounded text-xs">Tab</kbd> - Navigate between actions</li>
                </ul>
              </div>
            </details>
          </main>
        </div>
      </body>
    </html>
  ;
}
