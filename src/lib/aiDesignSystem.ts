// AI-Powered Design System Integration
// This module provides intelligent design system capabilities for the UI Agent

export interface DesignToken {
  name: string;
  value: string;
  category: 'color' | 'spacing' | 'typography' | 'shadow' | 'border' | 'animation';
  description?: string;
}

export interface ComponentAnalysis {
  complexity: 'simple' | 'moderate' | 'complex';
  suggestedProps: Record<string, any>;
  accessibility: {
    score: number;
    suggestions: string[];
  };
  performance: {
    score: number;
    optimizations: string[];
  };
  responsive: boolean;
  theme: 'light' | 'dark' | 'gaming' | 'auto';
}

export interface DesignSystemConfig {
  theme: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
  };
  spacing: Record<string, string>;
  typography: Record<string, string>;
  components: Record<string, any>;
}

class AIDesignSystem {
  private designTokens: DesignToken[] = [
    // Gaming Theme Colors
    { name: 'neon-cyan', value: '#00ffff', category: 'color', description: 'Bright cyan for highlights' },
    { name: 'neon-purple', value: '#8b5cf6', category: 'color', description: 'Electric purple for primary actions' },
    { name: 'neon-pink', value: '#ff00ff', category: 'color', description: 'Hot pink for accents' },
    { name: 'gaming-dark', value: '#0f172a', category: 'color', description: 'Deep dark background' },
    { name: 'gaming-darker', value: '#020617', category: 'color', description: 'Darker background variant' },
    
    // Spacing System
    { name: 'space-xs', value: '0.25rem', category: 'spacing', description: 'Extra small spacing' },
    { name: 'space-sm', value: '0.5rem', category: 'spacing', description: 'Small spacing' },
    { name: 'space-md', value: '1rem', category: 'spacing', description: 'Medium spacing' },
    { name: 'space-lg', value: '1.5rem', category: 'spacing', description: 'Large spacing' },
    { name: 'space-xl', value: '2rem', category: 'spacing', description: 'Extra large spacing' },
    
    // Typography
    { name: 'text-xs', value: '0.75rem', category: 'typography', description: 'Extra small text' },
    { name: 'text-sm', value: '0.875rem', category: 'typography', description: 'Small text' },
    { name: 'text-base', value: '1rem', category: 'typography', description: 'Base text size' },
    { name: 'text-lg', value: '1.125rem', category: 'typography', description: 'Large text' },
    { name: 'text-xl', value: '1.25rem', category: 'typography', description: 'Extra large text' },
    
    // Shadows
    { name: 'shadow-neon', value: '0 0 20px rgba(139, 92, 246, 0.5)', category: 'shadow', description: 'Neon glow effect' },
    { name: 'shadow-gaming', value: '0 25px 50px -12px rgba(0, 0, 0, 0.25)', category: 'shadow', description: 'Gaming card shadow' },
    
    // Animations
    { name: 'pulse-neon', value: 'pulse-neon 2s ease-in-out infinite', category: 'animation', description: 'Neon pulse animation' },
    { name: 'glow-rotate', value: 'glow-rotate 3s linear infinite', category: 'animation', description: 'Rotating glow effect' }
  ];

  private componentPatterns: Record<string, any> = {
    button: {
      base: 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
      variants: {
        gaming: 'bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white shadow-lg shadow-purple-500/25',
        neon: 'bg-transparent border-2 border-neon-purple text-neon-purple hover:bg-neon-purple hover:text-black animate-pulse-neon',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
      },
      sizes: {
        sm: 'h-9 px-3',
        md: 'h-10 py-2 px-4',
        lg: 'h-11 px-8',
      }
    },
    card: {
      base: 'rounded-lg border bg-card text-card-foreground shadow-sm',
      variants: {
        gaming: 'bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-xl border-purple-500/30 shadow-2xl shadow-purple-500/20',
        neon: 'bg-gaming-dark border-neon-purple shadow-neon animate-pulse-neon',
        glass: 'bg-white/10 backdrop-blur-lg border-white/20',
      }
    },
    input: {
      base: 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      variants: {
        gaming: 'bg-slate-700/50 backdrop-blur-sm border-purple-500/30 text-white placeholder-purple-300/60 focus:border-purple-400/60 focus:ring-purple-400/20',
      }
    }
  };

  analyzeUserRequest(request: string): ComponentAnalysis {
    const lowerRequest = request.toLowerCase();
    
    // Determine complexity based on keywords
    let complexity: 'simple' | 'moderate' | 'complex' = 'simple';
    if (lowerRequest.includes('dashboard') || lowerRequest.includes('table') || lowerRequest.includes('form')) {
      complexity = 'complex';
    } else if (lowerRequest.includes('card') || lowerRequest.includes('modal') || lowerRequest.includes('dropdown')) {
      complexity = 'moderate';
    }

    // Analyze theme preference
    let theme: 'light' | 'dark' | 'gaming' | 'auto' = 'auto';
    if (lowerRequest.includes('gaming') || lowerRequest.includes('neon') || lowerRequest.includes('glow')) {
      theme = 'gaming';
    } else if (lowerRequest.includes('dark')) {
      theme = 'dark';
    } else if (lowerRequest.includes('light')) {
      theme = 'light';
    }

    // Suggest props based on request
    const suggestedProps: Record<string, any> = {};
    if (lowerRequest.includes('large') || lowerRequest.includes('big')) {
      suggestedProps.size = 'lg';
    } else if (lowerRequest.includes('small') || lowerRequest.includes('mini')) {
      suggestedProps.size = 'sm';
    }

    if (theme === 'gaming') {
      suggestedProps.variant = 'gaming';
      suggestedProps.className = 'animate-pulse-neon';
    }

    // Accessibility analysis
    const accessibility = {
      score: this.calculateAccessibilityScore(request),
      suggestions: this.getAccessibilitySuggestions(request)
    };

    // Performance analysis
    const performance = {
      score: this.calculatePerformanceScore(complexity),
      optimizations: this.getPerformanceOptimizations(complexity)
    };

    return {
      complexity,
      suggestedProps,
      accessibility,
      performance,
      responsive: true, // Always suggest responsive design
      theme
    };
  }

  generateComponent(type: string, analysis: ComponentAnalysis): string {
    const pattern = this.componentPatterns[type];
    if (!pattern) {
      return this.generateCustomComponent(type, analysis);
    }

    const variant = analysis.theme === 'gaming' ? 'gaming' : 'default';
    const size = analysis.suggestedProps.size || 'md';
    
    let className = pattern.base;
    if (pattern.variants[variant]) {
      className += ` ${pattern.variants[variant]}`;
    }
    if (pattern.sizes && pattern.sizes[size]) {
      className += ` ${pattern.sizes[size]}`;
    }

    // Add responsive classes
    if (analysis.responsive) {
      className += ' w-full sm:w-auto';
    }

    // Add accessibility attributes
    const a11yProps = this.generateA11yProps(analysis);

    switch (type) {
      case 'button':
        return `<Button 
  className="${className}"
  ${a11yProps}
  ${analysis.suggestedProps.disabled ? 'disabled' : ''}
>
  ${analysis.suggestedProps.children || 'Button Text'}
</Button>`;

      case 'card':
        return `<Card className="${className}">
  <CardHeader>
    <CardTitle>${analysis.suggestedProps.title || 'Card Title'}</CardTitle>
    <CardDescription>
      ${analysis.suggestedProps.description || 'Card description goes here'}
    </CardDescription>
  </CardHeader>
  <CardContent>
    <p>Your content goes here</p>
  </CardContent>
</Card>`;

      case 'input':
        return `<Input
  className="${className}"
  placeholder="${analysis.suggestedProps.placeholder || 'Enter text...'}"
  ${a11yProps}
/>`;

      default:
        return this.generateCustomComponent(type, analysis);
    }
  }

  private generateCustomComponent(type: string, analysis: ComponentAnalysis): string {
    const baseClasses = analysis.theme === 'gaming' 
      ? 'bg-gradient-to-br from-slate-900/95 to-purple-900/95 backdrop-blur-xl border border-purple-500/30 rounded-2xl p-6 text-white'
      : 'bg-background border rounded-lg p-4';

    return `<div className="${baseClasses} ${analysis.responsive ? 'w-full' : ''}">
  <h3 className="font-bold text-lg mb-4">
    ${type.charAt(0).toUpperCase() + type.slice(1)} Component
  </h3>
  <p className="text-muted-foreground">
    Custom ${type} component with ${analysis.theme} theme
  </p>
</div>`;
  }

  private calculateAccessibilityScore(request: string): number {
    let score = 70; // Base score
    
    if (request.includes('accessible') || request.includes('a11y')) score += 20;
    if (request.includes('keyboard') || request.includes('focus')) score += 10;
    if (request.includes('screen reader') || request.includes('aria')) score += 15;
    
    return Math.min(score, 100);
  }

  private getAccessibilitySuggestions(request: string): string[] {
    const suggestions = [
      'Add proper ARIA labels',
      'Ensure keyboard navigation',
      'Use semantic HTML elements',
      'Provide sufficient color contrast'
    ];

    if (request.includes('form')) {
      suggestions.push('Add form validation messages', 'Use proper label associations');
    }

    if (request.includes('modal') || request.includes('dialog')) {
      suggestions.push('Implement focus trapping', 'Add escape key handling');
    }

    return suggestions;
  }

  private calculatePerformanceScore(complexity: string): number {
    switch (complexity) {
      case 'simple': return 95;
      case 'moderate': return 85;
      case 'complex': return 75;
      default: return 80;
    }
  }

  private getPerformanceOptimizations(complexity: string): string[] {
    const base = ['Use CSS animations over JS', 'Minimize re-renders', 'Lazy load when possible'];
    
    if (complexity === 'complex') {
      base.push('Consider virtualization', 'Implement code splitting', 'Use React.memo');
    }
    
    return base;
  }

  private generateA11yProps(analysis: ComponentAnalysis): string {
    const props = [];
    
    if (analysis.accessibility.score < 80) {
      props.push('aria-label="Component"');
    }
    
    if (analysis.complexity === 'complex') {
      props.push('role="region"');
    }
    
    return props.join(' ');
  }

  getDesignTokens(category?: string): DesignToken[] {
    if (category) {
      return this.designTokens.filter(token => token.category === category);
    }
    return this.designTokens;
  }

  suggestImprovements(code: string): string[] {
    const suggestions = [];
    
    if (!code.includes('className')) {
      suggestions.push('Add consistent styling with className');
    }
    
    if (!code.includes('aria-') && !code.includes('role=')) {
      suggestions.push('Improve accessibility with ARIA attributes');
    }
    
    if (code.includes('onClick') && !code.includes('onKeyDown')) {
      suggestions.push('Add keyboard event handlers for accessibility');
    }
    
    if (!code.includes('responsive') && !code.includes('sm:') && !code.includes('md:')) {
      suggestions.push('Consider responsive design patterns');
    }
    
    return suggestions;
  }
}

export const aiDesignSystem = new AIDesignSystem();
export default aiDesignSystem;